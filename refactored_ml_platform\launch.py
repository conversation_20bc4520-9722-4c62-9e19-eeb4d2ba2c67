#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习平台启动器
解决相对导入问题的启动脚本
"""

import sys
import os
from pathlib import Path

# 获取项目根目录
current_dir = Path(__file__).parent
project_root = current_dir.parent

# 添加到Python路径
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

def main():
    """主函数"""
    print("🚀 启动重构版多模型集成机器学习平台...")
    
    try:
        # 设置环境变量
        os.environ['PYTHONPATH'] = str(current_dir)
        
        # 导入必要的模块
        import tkinter as tk
        import logging
        
        print("✓ 基础模块导入成功")
        
        # 设置日志
        log_dir = current_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'app.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info("启动机器学习平台")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("多模型集成机器学习平台 - 重构版")
        root.geometry("1400x900")
        root.minsize(1200, 800)
        
        print("✓ 主窗口创建成功")
        
        # 尝试导入GUI模块
        try:
            # 使用绝对导入
            from gui.core.event_manager import get_event_manager
            from gui.core.config_manager import get_gui_config
            from gui.core.component_factory import get_component_factory
            
            print("✓ 核心GUI模块导入成功")
            
            # 初始化核心组件
            event_manager = get_event_manager()
            config = get_gui_config()
            component_factory = get_component_factory()
            
            print("✓ 核心组件初始化成功")
            
            # 导入主界面
            from gui.layouts.main_layout import MainWindow
            
            print("✓ 主界面模块导入成功")
            
            # 创建主界面
            main_window = MainWindow(root)
            
            print("✓ 主界面创建成功")
            print("✅ GUI启动完成！")
            
            # 启动GUI主循环
            root.mainloop()
            
        except ImportError as e:
            print(f"❌ GUI模块导入失败: {e}")
            print("启动简化GUI模式...")
            create_simple_gui(root)
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        show_cli_help()

def create_simple_gui(root):
    """创建简化GUI"""
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox

        # 主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="多模型集成机器学习平台", 
                               font=('Arial', 18, 'bold'))
        title_label.pack(pady=20)
        
        subtitle_label = ttk.Label(main_frame, text="重构版 - 简化模式", 
                                  font=('Arial', 12), foreground='gray')
        subtitle_label.pack()
        
        # 说明文本
        info_text = """
欢迎使用多模型集成机器学习平台！

当前运行在简化模式下。

完整功能请使用CLI命令行模式：

1. 查看帮助：
   python -m cli.main --help

2. 训练所有模型：
   python -m cli.main --data data.csv --target target --mode train --model All

3. 一键完整分析：
   python -m cli.pipeline --data data.csv --target target --strategy balanced

4. 生成可视化：
   python -m cli.main --data data.csv --target target --mode plot --model XGBoost

5. 生成报告：
   python -m cli.main --data data.csv --target target --mode report --model All
        """
        
        text_widget = tk.Text(main_frame, wrap=tk.WORD, height=20, width=80,
                             font=('Consolas', 10), bg='#f8f9fa', relief=tk.FLAT)
        text_widget.insert(1.0, info_text.strip())
        text_widget.config(state=tk.DISABLED)
        text_widget.pack(pady=20, fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        def show_cli_help():
            help_text = """
CLI命令行使用帮助

基本命令格式：
python -m cli.main [选项]

常用命令：

1. 训练单个模型：
python -m cli.main --data data.csv --target target --mode train --model RandomForest

2. 训练所有模型：
python -m cli.main --data data.csv --target target --mode train --model All

3. 生成可视化：
python -m cli.main --data data.csv --target target --mode plot --model XGBoost

4. 生成报告：
python -m cli.main --data data.csv --target target --mode report --model All

5. DeLong检验：
python -m cli.main --data data.csv --target target --mode delong --model RandomForest,XGBoost

6. 一键完整分析：
python -m cli.pipeline --data data.csv --target target --strategy balanced

参数说明：
--data: 数据文件路径
--target: 目标列名
--mode: 运行模式 (train/plot/report/delong)
--model: 模型名称 (All表示所有模型)
--strategy: 分析策略 (fast/balanced/comprehensive)

查看详细帮助：
python -m cli.main --help
python -m cli.pipeline --help
            """
            
            # 创建帮助窗口
            help_window = tk.Toplevel(root)
            help_window.title("CLI使用帮助")
            help_window.geometry("800x600")
            
            help_text_widget = tk.Text(help_window, wrap=tk.WORD, font=('Consolas', 10))
            help_scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=help_text_widget.yview)
            help_text_widget.configure(yscrollcommand=help_scrollbar.set)
            
            help_text_widget.insert(1.0, help_text.strip())
            help_text_widget.config(state=tk.DISABLED)
            
            help_text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            help_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        def open_project_dir():
            try:
                import subprocess
                import platform
                
                if platform.system() == 'Windows':
                    subprocess.run(['explorer', str(current_dir)])
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', str(current_dir)])
                else:  # Linux
                    subprocess.run(['xdg-open', str(current_dir)])
                    
            except Exception as e:
                messagebox.showerror("错误", f"无法打开项目目录: {e}")
        
        # 按钮
        ttk.Button(button_frame, text="📋 CLI使用帮助", 
                  command=show_cli_help).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="📂 打开项目目录", 
                  command=open_project_dir).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="❌ 退出", 
                  command=root.quit).pack(side=tk.LEFT, padx=10)
        
        print("✓ 简化GUI启动成功")
        
        # 启动GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 简化GUI启动失败: {e}")
        show_cli_help()

def show_cli_help():
    """显示CLI帮助信息"""
    print("\n" + "="*60)
    print("GUI启动失败，请使用CLI命令行模式:")
    print("="*60)
    print()
    print("1. 查看CLI帮助:")
    print("   python -m cli.main --help")
    print()
    print("2. 训练所有模型:")
    print("   python -m cli.main --data data.csv --target target --mode train --model All")
    print()
    print("3. 一键完整分析:")
    print("   python -m cli.pipeline --data data.csv --target target --strategy balanced")
    print()
    print("4. 生成可视化:")
    print("   python -m cli.main --data data.csv --target target --mode plot --model XGBoost")
    print()
    print("5. 生成报告:")
    print("   python -m cli.main --data data.csv --target target --mode report --model All")
    print()
    print("6. DeLong检验:")
    print("   python -m cli.main --data data.csv --target target --mode delong --model RandomForest,XGBoost")
    print()
    print("="*60)
    print("注意：请确保在 refactored_ml_platform 目录下运行命令")
    print("="*60)

if __name__ == '__main__':
    main()
