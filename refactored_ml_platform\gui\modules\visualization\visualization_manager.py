#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化管理器
负责数据和模型结果的可视化展示
"""

import logging
from typing import Dict, Any, Optional, List
import pandas as pd
import numpy as np

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
from ...components.enhanced_chart_widgets import EnhancedChartWidget


class VisualizationManager(BaseGUI):
    """可视化管理器类"""
    
    def __init__(self, parent):
        """初始化可视化管理器"""
        self.current_data = None
        self.current_results = None
        self.plot_widgets = {}
        
        super().__init__(parent)
        
        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = factory.create_frame(self.main_frame)
        title_frame.pack(fill='x', pady=(0, 10))
        
        factory.create_label(title_frame, text="数据可视化", style='title').pack(anchor='w')
        factory.create_label(title_frame, text="数据分析和模型结果的可视化展示", 
                           style='secondary').pack(anchor='w', pady=(5, 0))
        
        # 创建标签页
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # 数据探索标签页
        self._create_data_exploration_tab()
        
        # 模型结果标签页
        self._create_model_results_tab()
        
        # 自定义图表标签页
        self._create_custom_charts_tab()
    
    def _create_data_exploration_tab(self):
        """创建数据探索标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        exploration_frame = factory.create_frame(self.notebook)
        self.notebook.add(exploration_frame, text="数据探索")
        
        # 控制面板
        control_frame = factory.create_labelframe(exploration_frame, text="图表控制", style='section')
        control_frame.pack(fill='x', padx=10, pady=10)
        
        # 图表类型选择
        chart_type_frame = factory.create_frame(control_frame)
        chart_type_frame.pack(fill='x', pady=5)
        
        factory.create_label(chart_type_frame, text="图表类型:").pack(side='left')
        self.chart_type_var = factory.create_combobox(
            chart_type_frame, 
            values=['直方图', '散点图', '箱线图', '相关性热图', '分布图'],
            state='readonly'
        )
        self.chart_type_var.pack(side='left', padx=(10, 0))
        self.chart_type_var.set('直方图')
        
        # 列选择
        column_frame = factory.create_frame(control_frame)
        column_frame.pack(fill='x', pady=5)
        
        factory.create_label(column_frame, text="选择列:").pack(side='left')
        self.x_column_var = factory.create_combobox(column_frame, state='readonly')
        self.x_column_var.pack(side='left', padx=(10, 5))
        
        factory.create_label(column_frame, text="Y轴(可选):").pack(side='left', padx=(10, 0))
        self.y_column_var = factory.create_combobox(column_frame, state='readonly')
        self.y_column_var.pack(side='left', padx=(10, 0))
        
        # 生成按钮
        button_frame = factory.create_frame(control_frame)
        button_frame.pack(fill='x', pady=5)
        
        self.generate_chart_button = factory.create_button(
            button_frame, text="生成图表", command=self._generate_exploration_chart, style='primary'
        )
        self.generate_chart_button.pack(side='left')
        self.generate_chart_button.config(state='disabled')
        
        self.clear_chart_button = factory.create_button(
            button_frame, text="清空图表", command=self._clear_exploration_chart, style='secondary'
        )
        self.clear_chart_button.pack(side='left', padx=(10, 0))
        
        # 图表显示区域
        chart_frame = factory.create_labelframe(exploration_frame, text="图表显示", style='section')
        chart_frame.pack(fill='both', expand=True, padx=10, pady=10)
        # 数据探索图表显示区域
        self.exploration_chart_widget = EnhancedChartWidget(exploration_frame, figsize=(10, 6))
        self.plot_widgets['exploration'] = self.exploration_chart_widget
        
        # 图表组件已经包含了自己的滚动和导航功能
    
    def _create_model_results_tab(self):
        """创建模型结果标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        results_frame = factory.create_frame(self.notebook)
        self.notebook.add(results_frame, text="模型结果")
        
        # 结果类型选择
        result_control_frame = factory.create_labelframe(results_frame, text="结果可视化", style='section')
        result_control_frame.pack(fill='x', padx=10, pady=10)
        
        result_type_frame = factory.create_frame(result_control_frame)
        result_type_frame.pack(fill='x', pady=5)
        
        factory.create_label(result_type_frame, text="可视化类型:").pack(side='left')
        self.result_type_var = factory.create_combobox(
            result_type_frame,
            values=['模型性能对比', '特征重要性', '混淆矩阵', '学习曲线', 'ROC曲线'],
            state='readonly'
        )
        self.result_type_var.pack(side='left', padx=(10, 0))
        self.result_type_var.set('模型性能对比')
        
        # 生成按钮
        result_button_frame = factory.create_frame(result_control_frame)
        result_button_frame.pack(fill='x', pady=5)
        
        self.generate_result_button = factory.create_button(
            result_button_frame, text="生成结果图表", command=self._generate_result_chart, style='primary'
        )
        self.generate_result_button.pack(side='left')
        self.generate_result_button.config(state='disabled')
        
        self.clear_result_button = factory.create_button(
            result_button_frame, text="清空结果", command=self._clear_result_chart, style='secondary'
        )
        self.clear_result_button.pack(side='left', padx=(10, 0))
        
        # 结果显示区域
        result_display_frame = factory.create_labelframe(results_frame, text="结果显示", style='section')
        result_display_frame.pack(fill='both', expand=True, padx=10, pady=10)
        # 模型结果图表显示区域
        self.result_chart_widget = EnhancedChartWidget(results_frame, figsize=(10, 6))
        self.plot_widgets['results'] = self.result_chart_widget
        
        # 图表组件已经包含了自己的滚动和导航功能
    
    def _create_custom_charts_tab(self):
        """创建自定义图表标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        custom_frame = factory.create_frame(self.notebook)
        self.notebook.add(custom_frame, text="自定义图表")
        
        # 自定义设置
        custom_control_frame = factory.create_labelframe(custom_frame, text="自定义设置", style='section')
        custom_control_frame.pack(fill='x', padx=10, pady=10)
        
        # 图表标题
        title_frame = factory.create_frame(custom_control_frame)
        title_frame.pack(fill='x', pady=5)
        
        factory.create_label(title_frame, text="图表标题:").pack(side='left')
        self.chart_title_var = factory.create_entry(title_frame)
        self.chart_title_var.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        # 颜色主题
        theme_frame = factory.create_frame(custom_control_frame)
        theme_frame.pack(fill='x', pady=5)
        
        factory.create_label(theme_frame, text="颜色主题:").pack(side='left')
        self.color_theme_var = factory.create_combobox(
            theme_frame,
            values=['默认', '蓝色系', '绿色系', '红色系', '紫色系'],
            state='readonly'
        )
        self.color_theme_var.pack(side='left', padx=(10, 0))
        self.color_theme_var.set('默认')
        
        # 图表大小
        size_frame = factory.create_frame(custom_control_frame)
        size_frame.pack(fill='x', pady=5)
        
        factory.create_label(size_frame, text="图表大小:").pack(side='left')
        self.chart_width_var = factory.create_spinbox(size_frame, from_=400, to=1200, increment=50)
        self.chart_width_var.pack(side='left', padx=(10, 5))
        self.chart_width_var.delete(0, 'end')
        self.chart_width_var.insert(0, '800')
        
        factory.create_label(size_frame, text="x").pack(side='left', padx=5)
        self.chart_height_var = factory.create_spinbox(size_frame, from_=300, to=800, increment=50)
        self.chart_height_var.pack(side='left', padx=5)
        self.chart_height_var.delete(0, 'end')
        self.chart_height_var.insert(0, '600')
        
        # 生成按钮
        custom_button_frame = factory.create_frame(custom_control_frame)
        custom_button_frame.pack(fill='x', pady=5)
        
        self.generate_custom_button = factory.create_button(
            custom_button_frame, text="生成自定义图表", command=self._generate_custom_chart, style='primary'
        )
        self.generate_custom_button.pack(side='left')
        self.generate_custom_button.config(state='disabled')
        
        self.save_chart_button = factory.create_button(
            custom_button_frame, text="保存图表", command=self._save_chart, style='secondary'
        )
        self.save_chart_button.pack(side='left', padx=(10, 0))
        self.save_chart_button.config(state='disabled')
        
        # 自定义图表显示区域
        custom_display_frame = factory.create_labelframe(custom_frame, text="自定义图表", style='section')
        custom_display_frame.pack(fill='both', expand=True, padx=10, pady=10)
        # 自定义图表显示区域
        self.custom_chart_widget = EnhancedChartWidget(custom_frame, figsize=(10, 6))
        self.plot_widgets['custom'] = self.custom_chart_widget
        
        # 图表组件已经包含了自己的滚动和导航功能
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据和模型相关事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.MODEL_TRAINED, self._on_model_trained)
        self.subscribe_event(EventTypes.VISUALIZATION_REQUESTED, self._on_visualization_requested)
    
    def _generate_exploration_chart(self):
        """生成数据探索图表"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            chart_type = self.chart_type_var.get()
            x_column = self.x_column_var.get()
            y_column = self.y_column_var.get() if self.y_column_var.get() else None
            
            if not x_column:
                self.show_warning("警告", "请选择X轴列")
                return
            
            # 使用增强图表组件生成图表
            success = self.exploration_chart_widget.create_data_exploration_chart(
                data=self.current_data,
                chart_type=chart_type,
                x_col=x_column,
                y_col=y_column
            )
            
            if success:
                self.show_info("成功", f"已生成{chart_type}图表")
            else:
                self.show_error("错误", "图表生成失败")
            
        except Exception as e:
            self.show_error("错误", f"生成图表时出错: {str(e)}")
    
    def _generate_result_chart(self):
        """生成模型结果图表"""
        if self.current_results is None:
            self.show_warning("警告", "请先训练模型")
            return
        
        try:
            result_type = self.result_type_var.get()
            
            # 使用增强图表组件生成模型结果图表
            success = self.result_chart_widget.create_model_result_chart(
                result_type=result_type,
                model_results=self.current_results
            )
            
            if success:
                self.show_info("成功", f"已生成{result_type}图表")
            else:
                self.show_error("错误", "模型结果图表生成失败")
            
        except Exception as e:
            self.show_error("错误", f"生成结果图表时出错: {str(e)}")
    
    def _generate_custom_chart(self):
        """生成自定义图表"""
        if self.current_data is None:
            self.show_warning("警告", "请先加载数据")
            return
        
        try:
            title = self.chart_title_var.get() or "自定义图表"
            theme = self.color_theme_var.get()
            width = self.chart_width_var.get() or "800"
            height = self.chart_height_var.get() or "600"
            
            # 使用默认的数据探索图表作为自定义图表
            numeric_cols = self.current_data.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                success = self.custom_chart_widget.create_data_exploration_chart(
                    data=self.current_data,
                    chart_type='correlation',
                    x_col=numeric_cols[0]
                )
            else:
                success = False
            
            if success:
                self.save_chart_button.config(state='normal')
                self.show_info("成功", "已生成自定义图表")
            else:
                self.show_error("错误", "自定义图表生成失败")
            
        except Exception as e:
            self.show_error("错误", f"生成自定义图表时出错: {str(e)}")
    
    def _generate_chart_description(self, chart_type: str, x_column: str, y_column: str = None) -> str:
        """生成图表描述（模拟实际可视化）"""
        if self.current_data is None:
            return "无数据可显示"
        
        data_info = f"数据形状: {self.current_data.shape}\n"
        data_info += f"X轴列: {x_column}\n"
        if y_column:
            data_info += f"Y轴列: {y_column}\n"
        
        if chart_type == '直方图':
            if x_column in self.current_data.columns:
                values = self.current_data[x_column]
                if pd.api.types.is_numeric_dtype(values):
                    stats = f"""
{chart_type} - {x_column}
{data_info}
统计信息:
- 均值: {values.mean():.2f}
- 标准差: {values.std():.2f}
- 最小值: {values.min():.2f}
- 最大值: {values.max():.2f}
- 中位数: {values.median():.2f}

分布特征:
- 数据点数: {len(values)}
- 非空值: {values.count()}
- 缺失值: {values.isnull().sum()}

注: 实际应用中这里会显示直方图可视化
"""
                else:
                    unique_vals = values.value_counts()
                    stats = f"""
{chart_type} - {x_column} (分类数据)
{data_info}
分类统计:
{unique_vals.head(10).to_string()}

注: 实际应用中这里会显示条形图可视化
"""
                return stats
        
        elif chart_type == '散点图' and y_column:
            if x_column in self.current_data.columns and y_column in self.current_data.columns:
                x_vals = self.current_data[x_column]
                y_vals = self.current_data[y_column]
                
                if pd.api.types.is_numeric_dtype(x_vals) and pd.api.types.is_numeric_dtype(y_vals):
                    correlation = x_vals.corr(y_vals)
                    return f"""
{chart_type} - {x_column} vs {y_column}
{data_info}
相关性分析:
- 相关系数: {correlation:.3f}
- X轴范围: {x_vals.min():.2f} ~ {x_vals.max():.2f}
- Y轴范围: {y_vals.min():.2f} ~ {y_vals.max():.2f}

注: 实际应用中这里会显示散点图可视化
"""
        
        elif chart_type == '相关性热图':
            numeric_cols = self.current_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                corr_matrix = self.current_data[numeric_cols].corr()
                return f"""
{chart_type}
{data_info}
数值列相关性矩阵:
{corr_matrix.round(3).to_string()}

注: 实际应用中这里会显示热图可视化
"""
        
        return f"""
{chart_type}
{data_info}
图表类型: {chart_type}
选择的列: {x_column}
{f'Y轴列: {y_column}' if y_column else ''}

注: 实际应用中这里会显示相应的可视化图表
"""
    
    def _generate_result_description(self, result_type: str) -> str:
        """生成结果描述（模拟实际可视化）"""
        if self.current_results is None:
            return "无模型结果可显示"
        
        base_info = f"结果类型: {result_type}\n"
        base_info += f"模型数量: {len(self.current_results.get('models', {}))}\n"
        
        if result_type == '模型性能对比':
            models_info = ""
            for model_name, model_data in self.current_results.get('models', {}).items():
                if 'metrics' in model_data:
                    metrics = model_data['metrics']
                    models_info += f"\n{model_name}:\n"
                    for metric, value in metrics.items():
                        models_info += f"  - {metric}: {value:.4f}\n"
            
            return f"""
{result_type}
{base_info}
性能指标对比:
{models_info}

注: 实际应用中这里会显示性能对比柱状图
"""
        
        elif result_type == '特征重要性':
            return f"""
{result_type}
{base_info}
特征重要性分析:
- 显示各特征对模型预测的重要程度
- 帮助理解模型决策过程
- 支持特征选择和模型优化

注: 实际应用中这里会显示特征重要性条形图
"""
        
        return f"""
{result_type}
{base_info}
结果可视化类型: {result_type}

注: 实际应用中这里会显示相应的结果可视化图表
"""
    
    def _generate_custom_description(self, title: str, theme: str, width: str, height: str) -> str:
        """生成自定义图表描述"""
        return f"""
自定义图表设置
==================
标题: {title}
颜色主题: {theme}
图表大小: {width} x {height}
数据源: {self.current_data.shape if self.current_data is not None else '无数据'}

自定义配置:
- 支持多种图表类型
- 可调整颜色主题
- 自定义图表尺寸
- 灵活的数据展示

注: 实际应用中这里会显示根据设置生成的自定义图表
"""
    
    def _clear_exploration_chart(self):
        """清空探索图表"""
        self.exploration_chart_widget.clear_chart()
    
    def _clear_result_chart(self):
        """清空结果图表"""
        self.result_chart_widget.clear_chart()
    
    def _save_chart(self):
        """保存图表"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存图表",
                defaultextension=".png",
                filetypes=[("PNG文件", "*.png"), ("JPG文件", "*.jpg"), ("所有文件", "*.*")]
            )
            
            if filename:
                # 模拟保存图表
                self.show_info("成功", f"图表已保存到: {filename}")
                
        except Exception as e:
            self.show_error("错误", f"保存图表时出错: {str(e)}")
    
    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            # 更新当前数据
            if 'dataframe' in event_data:
                self.current_data = event_data['dataframe']
                
                # 更新列选择器
                columns = list(self.current_data.columns)
                self.x_column_var['values'] = columns
                self.y_column_var['values'] = [''] + columns
                
                # 启用按钮
                self.generate_chart_button.config(state='normal')
                self.generate_custom_button.config(state='normal')
                
                self.logger.info(f"可视化模块已加载数据: {self.current_data.shape}")
                
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
    
    def _on_model_trained(self, event_data):
        """模型训练事件处理"""
        try:
            # 更新当前结果
            self.current_results = event_data
            
            # 启用结果图表按钮
            self.generate_result_button.config(state='normal')
            
            self.logger.info("可视化模块已接收模型训练结果")
            
        except Exception as e:
            self.logger.error(f"处理模型训练事件时出错: {e}")
    
    def _on_visualization_requested(self, event_data):
        """可视化请求事件处理"""
        try:
            viz_type = event_data.get('type', 'data_exploration')
            
            if viz_type == 'data_exploration':
                self.notebook.select(0)  # 切换到数据探索标签页
            elif viz_type == 'model_results':
                self.notebook.select(1)  # 切换到模型结果标签页
            elif viz_type == 'custom':
                self.notebook.select(2)  # 切换到自定义图表标签页
                
        except Exception as e:
            self.logger.error(f"处理可视化请求事件时出错: {e}")
    
    def get_current_data(self) -> Optional[pd.DataFrame]:
        """获取当前数据"""
        return self.current_data
    
    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """获取当前结果"""
        return self.current_results
