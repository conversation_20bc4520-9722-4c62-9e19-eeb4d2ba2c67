#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习平台启动脚本
简化的启动入口，避免导入问题
"""

import sys
import os
from pathlib import Path

# 获取项目根目录
current_dir = Path(__file__).parent
project_root = current_dir.parent

# 添加到Python路径
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

def main():
    """主函数"""
    try:
        print("🚀 启动机器学习平台...")
        
        # 检查tkinter是否可用
        try:
            import tkinter as tk
            print("✓ Tkinter 可用")
        except ImportError:
            print("❌ Tkinter 不可用，请安装tkinter")
            return
        
        # 尝试导入GUI模块
        try:
            print("正在加载GUI模块...")

            # 直接使用简化的GUI模块
            from gui.main_window import MainWindow
            print("✓ GUI模块加载成功")

        except ImportError as e:
            print(f"❌ GUI模块导入失败: {e}")
            print("尝试使用最简化的GUI启动...")

            # 使用最简化的GUI启动
            return start_simple_gui()

        # 创建主窗口
        print("正在创建主窗口...")
        root = tk.Tk()

        # 创建主界面
        app = MainWindow(root)
        
        print("✓ GUI启动成功")
        
        # 启动GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 尝试CLI模式
        print("\n尝试启动CLI模式...")
        start_cli_mode()

def start_simple_gui():
    """启动简化的GUI"""
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        
        print("启动简化GUI模式...")
        
        root = tk.Tk()
        root.title("机器学习平台 - 简化模式")
        root.geometry("800x600")
        
        # 创建简单界面
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame, text="机器学习平台", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=20)
        
        # 说明
        info_text = """
欢迎使用机器学习平台！

当前运行在简化模式下。
完整功能正在加载中...

您可以：
1. 关闭此窗口并使用命令行模式
2. 检查依赖是否正确安装
3. 查看错误日志获取更多信息
        """
        
        info_label = ttk.Label(main_frame, text=info_text.strip(), 
                              justify=tk.LEFT)
        info_label.pack(pady=20)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        def show_cli_help():
            help_text = """
命令行使用方法：

# 训练模型
python -m refactored_ml_platform.cli.main --data data.csv --target target --mode train --model All

# 一键分析
python -m refactored_ml_platform.cli.pipeline --data data.csv --target target --strategy balanced

# 查看帮助
python -m refactored_ml_platform.cli.main --help
            """
            messagebox.showinfo("CLI使用帮助", help_text)
        
        ttk.Button(button_frame, text="CLI使用帮助", 
                  command=show_cli_help).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(button_frame, text="退出", 
                  command=root.quit).pack(side=tk.LEFT, padx=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 简化GUI启动失败: {e}")

def start_cli_mode():
    """启动CLI模式"""
    try:
        print("=" * 50)
        print("CLI模式使用说明:")
        print("=" * 50)
        print()
        print("1. 训练所有模型:")
        print("   python -m refactored_ml_platform.cli.main --data data.csv --target target --mode train --model All")
        print()
        print("2. 一键完整分析:")
        print("   python -m refactored_ml_platform.cli.pipeline --data data.csv --target target --strategy balanced")
        print()
        print("3. 查看详细帮助:")
        print("   python -m refactored_ml_platform.cli.main --help")
        print("   python -m refactored_ml_platform.cli.pipeline --help")
        print()
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ CLI模式启动失败: {e}")

if __name__ == '__main__':
    main()
