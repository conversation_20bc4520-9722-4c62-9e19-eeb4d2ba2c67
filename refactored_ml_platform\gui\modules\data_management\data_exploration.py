#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据探索模块
提供数据探索和统计分析的GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any

try:
    from ....utils.data_explorer import DataExplorer
except ImportError:
    # 备用导入方式
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent.parent.parent))
    from utils.data_explorer import DataExplorer
from ....core.event_manager import get_event_manager
from ....utils.error_handler import get_error_handler


class DataExplorationModule:
    """数据探索模块"""
    
    def __init__(self, parent):
        """初始化数据探索模块"""
        self.parent = parent
        self.event_manager = get_event_manager()
        self.error_handler = get_error_handler()
        
        # 数据探索器
        self.explorer = DataExplorer()
        
        # 数据存储
        self.current_df = None
        self.target_column = None
        self.analysis_results = {}
        
        # GUI组件
        self.frame = None
        self.notebook = None
        self.canvas = None
        self.fig = None
        self.ax = None
        
        # 控制变量
        self.data_source_var = tk.StringVar(value="current")
        self.file_path_var = tk.StringVar()
        self.target_var = tk.StringVar()
        self.analysis_type_var = tk.StringVar(value="basic_info")
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面
        self._create_interface()
        
        # 注册事件监听
        self._register_events()
    
    def _create_interface(self):
        """创建界面"""
        # 主框架
        self.frame = ttk.Frame(self.parent)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡
        self._create_data_tab()
        self._create_analysis_tab()
        self._create_visualization_tab()
        self._create_results_tab()
    
    def _create_data_tab(self):
        """创建数据选项卡"""
        data_tab = ttk.Frame(self.notebook)
        self.notebook.add(data_tab, text="📊 数据选择")
        
        # 数据源选择
        data_source_frame = ttk.LabelFrame(data_tab, text="数据源选择")
        data_source_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Radiobutton(data_source_frame, text="使用当前加载的数据", 
                       variable=self.data_source_var, value="current").pack(anchor=tk.W, padx=10, pady=5)
        ttk.Radiobutton(data_source_frame, text="选择新的数据文件", 
                       variable=self.data_source_var, value="new").pack(anchor=tk.W, padx=10, pady=5)
        
        # 文件选择
        file_frame = ttk.Frame(data_source_frame)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(file_frame, text="文件路径:").pack(side=tk.LEFT)
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        ttk.Button(file_frame, text="浏览...", command=self._browse_file).pack(side=tk.RIGHT, padx=5)
        
        # 目标变量选择
        target_frame = ttk.LabelFrame(data_tab, text="目标变量选择")
        target_frame.pack(fill=tk.X, padx=10, pady=10)
        
        target_select_frame = ttk.Frame(target_frame)
        target_select_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(target_select_frame, text="目标变量:").pack(side=tk.LEFT)
        self.target_combo = ttk.Combobox(target_select_frame, textvariable=self.target_var, 
                                        state="readonly", width=20)
        self.target_combo.pack(side=tk.LEFT, padx=10)
        
        # 加载数据按钮
        ttk.Button(target_frame, text="📥 加载数据", command=self._load_data).pack(pady=10)
        
        # 数据信息显示
        info_frame = ttk.LabelFrame(data_tab, text="数据信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.data_info_text = tk.Text(info_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.data_info_text.yview)
        self.data_info_text.configure(yscrollcommand=scrollbar.set)
        
        self.data_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def _create_analysis_tab(self):
        """创建分析选项卡"""
        analysis_tab = ttk.Frame(self.notebook)
        self.notebook.add(analysis_tab, text="🔍 统计分析")
        
        # 分析类型选择
        type_frame = ttk.LabelFrame(analysis_tab, text="分析类型")
        type_frame.pack(fill=tk.X, padx=10, pady=10)
        
        analysis_types = [
            ("基本信息", "basic_info"),
            ("描述性统计", "descriptive_stats"),
            ("相关性分析", "correlation"),
            ("分组概率分析", "group_probabilities"),
            ("卡方检验", "chi_square")
        ]
        
        for i, (text, value) in enumerate(analysis_types):
            row = i // 3
            col = i % 3
            ttk.Radiobutton(type_frame, text=text, variable=self.analysis_type_var, 
                           value=value).grid(row=row, column=col, sticky=tk.W, padx=10, pady=5)
        
        # 分析参数配置
        param_frame = ttk.LabelFrame(analysis_tab, text="参数配置")
        param_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 相关性方法选择
        corr_frame = ttk.Frame(param_frame)
        corr_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(corr_frame, text="相关性方法:").pack(side=tk.LEFT)
        self.corr_method_var = tk.StringVar(value="pearson")
        corr_methods = ["pearson", "spearman", "kendall"]
        self.corr_method_combo = ttk.Combobox(corr_frame, textvariable=self.corr_method_var,
                                             values=corr_methods, state="readonly", width=15)
        self.corr_method_combo.pack(side=tk.LEFT, padx=10)
        
        # 分箱数量设置
        bins_frame = ttk.Frame(param_frame)
        bins_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(bins_frame, text="分箱数量:").pack(side=tk.LEFT)
        self.bins_var = tk.StringVar(value="5")
        self.bins_spinbox = tk.Spinbox(bins_frame, textvariable=self.bins_var,
                                      from_=3, to=20, width=10)
        self.bins_spinbox.pack(side=tk.LEFT, padx=10)
        
        # 执行分析按钮
        ttk.Button(analysis_tab, text="🚀 执行分析", command=self._run_analysis).pack(pady=20)
    
    def _create_visualization_tab(self):
        """创建可视化选项卡"""
        viz_tab = ttk.Frame(self.notebook)
        self.notebook.add(viz_tab, text="📈 可视化")
        
        # 图表类型选择
        chart_frame = ttk.LabelFrame(viz_tab, text="图表类型")
        chart_frame.pack(fill=tk.X, padx=10, pady=10)
        
        chart_types = [
            ("相关性热力图", "correlation_heatmap"),
            ("变量分布图", "distribution"),
            ("分组概率图", "group_probability")
        ]
        
        self.chart_type_var = tk.StringVar(value="correlation_heatmap")
        for text, value in chart_types:
            ttk.Radiobutton(chart_frame, text=text, variable=self.chart_type_var, 
                           value=value).pack(anchor=tk.W, padx=10, pady=5)
        
        # 变量选择
        var_frame = ttk.LabelFrame(viz_tab, text="变量选择")
        var_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(var_frame, text="选择变量:").pack(side=tk.LEFT, padx=10)
        self.viz_var = tk.StringVar()
        self.viz_var_combo = ttk.Combobox(var_frame, textvariable=self.viz_var,
                                         state="readonly", width=20)
        self.viz_var_combo.pack(side=tk.LEFT, padx=10)
        
        # 生成图表按钮
        ttk.Button(viz_tab, text="📊 生成图表", command=self._generate_chart).pack(pady=20)
        
        # 图表显示区域
        chart_display_frame = ttk.LabelFrame(viz_tab, text="图表显示")
        chart_display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建matplotlib图表
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.fig.patch.set_facecolor('white')
        
        self.canvas = FigureCanvasTkAgg(self.fig, chart_display_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        toolbar_frame = ttk.Frame(chart_display_frame)
        toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame)
        self.toolbar.update()
        
        # 保存图表按钮
        ttk.Button(chart_display_frame, text="💾 保存图表", command=self._save_chart).pack(pady=5)
    
    def _create_results_tab(self):
        """创建结果选项卡"""
        results_tab = ttk.Frame(self.notebook)
        self.notebook.add(results_tab, text="📋 分析结果")
        
        # 结果显示区域
        self.results_text = tk.Text(results_tab, wrap=tk.WORD, font=('Consolas', 10))
        results_scrollbar = ttk.Scrollbar(results_tab, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 导出结果按钮
        export_frame = ttk.Frame(results_tab)
        export_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(export_frame, text="📤 导出结果", command=self._export_results).pack(side=tk.LEFT)
        ttk.Button(export_frame, text="🗑️ 清空结果", command=self._clear_results).pack(side=tk.LEFT, padx=10)
        
        # 状态栏
        status_frame = ttk.Frame(results_tab)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(status_frame, text="状态:").pack(side=tk.LEFT)
        ttk.Label(status_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.LEFT, padx=5)
    
    def _register_events(self):
        """注册事件监听"""
        self.event_manager.subscribe('data_loaded', self._on_data_loaded)
    
    def _on_data_loaded(self, event_data):
        """数据加载完成事件处理"""
        df = event_data.get('dataframe')
        if df is not None:
            self.current_df = df
            self._update_column_lists()
            self._display_data_info()
    
    def _browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if filename:
            self.file_path_var.set(filename)
    
    def _load_data(self):
        """加载数据"""
        try:
            if self.data_source_var.get() == "current":
                if self.current_df is None:
                    messagebox.showwarning("警告", "当前没有加载的数据")
                    return
            else:
                file_path = self.file_path_var.get()
                if not file_path:
                    messagebox.showwarning("警告", "请选择数据文件")
                    return
                
                # 加载新数据
                if file_path.endswith('.csv'):
                    self.current_df = pd.read_csv(file_path)
                elif file_path.endswith('.xlsx'):
                    self.current_df = pd.read_excel(file_path)
                else:
                    messagebox.showerror("错误", "不支持的文件格式")
                    return
            
            self._update_column_lists()
            self._display_data_info()
            messagebox.showinfo("成功", "数据加载完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {e}")
    
    def _update_column_lists(self):
        """更新列名列表"""
        if self.current_df is not None:
            columns = list(self.current_df.columns)
            self.target_combo['values'] = columns
            self.viz_var_combo['values'] = columns
    
    def _display_data_info(self):
        """显示数据信息"""
        if self.current_df is None:
            return
        
        info = self.explorer.analyze_basic_info(self.current_df)
        
        info_text = f"""数据形状: {info['shape']}
内存使用: {info['memory_usage']:.2f} MB
重复行数: {info['duplicates']}

列信息:
"""
        
        for col in info['columns']:
            dtype = info['dtypes'][col]
            missing = info['missing_values'][col]
            missing_pct = info['missing_percentage'][col]
            info_text += f"  {col}: {dtype}, 缺失值: {missing} ({missing_pct:.1f}%)\n"
        
        self.data_info_text.delete(1.0, tk.END)
        self.data_info_text.insert(1.0, info_text)
    
    def _run_analysis(self):
        """运行分析"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        # 在后台线程中运行分析
        threading.Thread(target=self._perform_analysis, daemon=True).start()
    
    def _perform_analysis(self):
        """执行分析"""
        try:
            self.status_var.set("正在执行分析...")
            
            analysis_type = self.analysis_type_var.get()
            
            if analysis_type == "basic_info":
                result = self.explorer.analyze_basic_info(self.current_df)
            elif analysis_type == "descriptive_stats":
                result = self.explorer.analyze_descriptive_stats(self.current_df)
            elif analysis_type == "correlation":
                method = self.corr_method_var.get()
                result = self.explorer.analyze_correlation(self.current_df, method)
            elif analysis_type == "group_probabilities":
                target_col = self.target_var.get()
                if not target_col:
                    messagebox.showwarning("警告", "请选择目标变量")
                    return
                
                numeric_cols = self.current_df.select_dtypes(include=[np.number]).columns.tolist()
                bins = int(self.bins_var.get())
                result = self.explorer.analyze_group_probabilities(
                    self.current_df, target_col, numeric_cols, bins
                )
            else:
                messagebox.showinfo("提示", "该分析类型尚未实现")
                return
            
            # 保存结果
            self.analysis_results[analysis_type] = result
            
            # 显示结果
            self._display_analysis_result(analysis_type, result)
            
            self.status_var.set("分析完成")
            
        except Exception as e:
            self.error_handler.handle_error(e, "数据探索分析")
            self.status_var.set("分析失败")
    
    def _display_analysis_result(self, analysis_type: str, result: Any):
        """显示分析结果"""
        self.results_text.delete(1.0, tk.END)
        
        result_text = f"=== {analysis_type} 分析结果 ===\n\n"
        
        if isinstance(result, dict):
            for key, value in result.items():
                result_text += f"{key}:\n{value}\n\n"
        elif isinstance(result, pd.DataFrame):
            result_text += str(result)
        else:
            result_text += str(result)
        
        self.results_text.insert(1.0, result_text)
    
    def _generate_chart(self):
        """生成图表"""
        if self.current_df is None:
            messagebox.showwarning("警告", "请先加载数据")
            return
        
        try:
            chart_type = self.chart_type_var.get()
            
            self.ax.clear()
            
            if chart_type == "correlation_heatmap":
                if 'correlation' in self.analysis_results:
                    corr_matrix = self.analysis_results['correlation']
                else:
                    corr_matrix = self.explorer.analyze_correlation(self.current_df)
                
                if not corr_matrix.empty:
                    self.explorer.create_correlation_heatmap(corr_matrix)
                    # 这里需要将生成的图表复制到当前画布
                    self.ax.text(0.5, 0.5, '相关性热力图\n(请在可视化窗口中查看)', 
                               ha='center', va='center', transform=self.ax.transAxes)
                else:
                    self.ax.text(0.5, 0.5, '数据不足，无法生成相关性热力图', 
                               ha='center', va='center', transform=self.ax.transAxes)
            
            elif chart_type == "distribution":
                var_name = self.viz_var.get()
                if not var_name:
                    messagebox.showwarning("警告", "请选择变量")
                    return
                
                if var_name in self.current_df.columns:
                    self.explorer.create_distribution_plot(self.current_df, var_name)
                    self.ax.text(0.5, 0.5, f'{var_name} 分布图\n(请在可视化窗口中查看)', 
                               ha='center', va='center', transform=self.ax.transAxes)
            
            self.canvas.draw()
            
        except Exception as e:
            self.error_handler.handle_error(e, "生成图表")
    
    def _save_chart(self):
        """保存图表"""
        filename = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[("PNG文件", "*.png"), ("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")
    
    def _export_results(self):
        """导出结果"""
        filename = filedialog.asksaveasfilename(
            title="导出分析结果",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.results_text.get(1.0, tk.END))
                messagebox.showinfo("成功", f"结果已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
    
    def _clear_results(self):
        """清空结果"""
        self.results_text.delete(1.0, tk.END)
        self.analysis_results.clear()
    
    def get_frame(self):
        """获取主框架"""
        return self.frame
