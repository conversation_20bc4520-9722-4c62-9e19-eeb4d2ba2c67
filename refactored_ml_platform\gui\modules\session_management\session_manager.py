#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理器
负责项目会话的保存、加载和管理
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import EventTypes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from utils.file_utils import FileUtils


class SessionManager(BaseGUI):
    """会话管理器类"""
    
    def __init__(self, parent):
        """初始化会话管理器"""
        self.current_session = None
        self.session_data = {}
        self.sessions_dir = FileUtils.get_project_root() / 'sessions'
        self.sessions_dir.mkdir(exist_ok=True)
        
        super().__init__(parent)
        
        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        self.main_frame = factory.create_frame(self.parent)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 标题
        title_frame = factory.create_frame(self.main_frame)
        title_frame.pack(fill='x', pady=(0, 10))
        
        factory.create_label(title_frame, text="会话管理", style='title').pack(anchor='w')
        factory.create_label(title_frame, text="管理项目会话的保存和加载", 
                           style='secondary').pack(anchor='w', pady=(5, 0))
        
        # 创建标签页
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # 当前会话标签页
        self._create_current_session_tab()
        
        # 会话历史标签页
        self._create_session_history_tab()
        
        # 导入导出标签页
        self._create_import_export_tab()
    
    def _create_current_session_tab(self):
        """创建当前会话标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        session_frame = factory.create_frame(self.notebook)
        self.notebook.add(session_frame, text="当前会话")
        
        # 会话信息区域
        info_frame = factory.create_labelframe(session_frame, text="会话信息", style='section')
        info_frame.pack(fill='x', padx=10, pady=10)
        
        # 会话名称
        name_frame = factory.create_frame(info_frame)
        name_frame.pack(fill='x', pady=5)
        
        factory.create_label(name_frame, text="会话名称:").pack(side='left')
        self.session_name_var = factory.create_entry(name_frame)
        self.session_name_var.pack(side='left', fill='x', expand=True, padx=(10, 0))
        
        # 会话描述
        desc_frame = factory.create_frame(info_frame)
        desc_frame.pack(fill='both', expand=True, pady=5)
        
        factory.create_label(desc_frame, text="会话描述:").pack(anchor='w')
        self.session_desc_text = factory.create_text(desc_frame, height=4)
        self.session_desc_text.pack(fill='both', expand=True, pady=(5, 0))
        
        # 操作按钮
        button_frame = factory.create_frame(session_frame)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        self.save_button = factory.create_button(
            button_frame, text="保存会话", command=self._save_current_session, style='primary'
        )
        self.save_button.pack(side='left', padx=(0, 10))
        
        self.new_button = factory.create_button(
            button_frame, text="新建会话", command=self._new_session, style='secondary'
        )
        self.new_button.pack(side='left', padx=(0, 10))
        
        self.clear_button = factory.create_button(
            button_frame, text="清空会话", command=self._clear_session, style='secondary'
        )
        self.clear_button.pack(side='left')
    
    def _create_session_history_tab(self):
        """创建会话历史标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        history_frame = factory.create_frame(self.notebook)
        self.notebook.add(history_frame, text="会话历史")
        
        # 会话列表
        list_frame = factory.create_labelframe(history_frame, text="历史会话", style='section')
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建列表框
        columns = ['会话名称', '创建时间', '修改时间', '描述']
        self.history_tree = factory.create_treeview(
            list_frame, columns=columns, show='headings'
        )
        
        # 设置列
        for col in columns:
            self.history_tree.heading(col, text=col)
            if col == '会话名称':
                self.history_tree.column(col, width=150)
            elif col in ['创建时间', '修改时间']:
                self.history_tree.column(col, width=120)
            else:
                self.history_tree.column(col, width=200)
        
        # 滚动条
        history_scrollbar = factory.create_scrollbar(list_frame, orient='vertical')
        history_scrollbar.pack(side='right', fill='y')
        self.history_tree.pack(side='left', fill='both', expand=True)
        
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        history_scrollbar.configure(command=self.history_tree.yview)
        
        # 绑定选择事件
        self.history_tree.bind('<<TreeviewSelect>>', self._on_history_select)
        
        # 操作按钮
        history_button_frame = factory.create_frame(history_frame)
        history_button_frame.pack(fill='x', padx=10, pady=10)
        
        self.load_button = factory.create_button(
            history_button_frame, text="加载会话", command=self._load_selected_session, style='primary'
        )
        self.load_button.pack(side='left', padx=(0, 10))
        self.load_button.config(state='disabled')
        
        self.delete_button = factory.create_button(
            history_button_frame, text="删除会话", command=self._delete_selected_session, style='secondary'
        )
        self.delete_button.pack(side='left', padx=(0, 10))
        self.delete_button.config(state='disabled')
        
        self.refresh_button = factory.create_button(
            history_button_frame, text="刷新列表", command=self._refresh_history_list, style='secondary'
        )
        self.refresh_button.pack(side='left')
        
        # 初始加载历史列表
        self._refresh_history_list()
    
    def _create_import_export_tab(self):
        """创建导入导出标签页"""
        factory = get_component_factory()
        
        # 创建标签页框架
        ie_frame = factory.create_frame(self.notebook)
        self.notebook.add(ie_frame, text="导入导出")
        
        # 导出区域
        export_frame = factory.create_labelframe(ie_frame, text="导出会话", style='section')
        export_frame.pack(fill='x', padx=10, pady=10)
        
        factory.create_label(export_frame, text="将当前会话导出为文件").pack(anchor='w', pady=5)
        
        export_button_frame = factory.create_frame(export_frame)
        export_button_frame.pack(fill='x', pady=5)
        
        self.export_button = factory.create_button(
            export_button_frame, text="导出会话", command=self._export_session, style='primary'
        )
        self.export_button.pack(side='left')
        
        # 导入区域
        import_frame = factory.create_labelframe(ie_frame, text="导入会话", style='section')
        import_frame.pack(fill='x', padx=10, pady=10)
        
        factory.create_label(import_frame, text="从文件导入会话").pack(anchor='w', pady=5)
        
        import_button_frame = factory.create_frame(import_frame)
        import_button_frame.pack(fill='x', pady=5)
        
        self.import_button = factory.create_button(
            import_button_frame, text="导入会话", command=self._import_session, style='primary'
        )
        self.import_button.pack(side='left')
    
    def _bind_events(self):
        """绑定事件"""
        # 订阅数据相关事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.MODEL_TRAINED, self._on_model_trained)
        self.subscribe_event(EventTypes.SESSION_CHANGED, self._on_session_changed)
    
    def _save_current_session(self):
        """保存当前会话"""
        try:
            session_name = self.session_name_var.get().strip()
            if not session_name:
                self.show_warning("警告", "请输入会话名称")
                return
            
            # 收集会话数据
            session_data = {
                'name': session_name,
                'description': self.session_desc_text.get(1.0, 'end-1c'),
                'created_time': datetime.now().isoformat(),
                'modified_time': datetime.now().isoformat(),
                'data': self._collect_session_data()
            }
            
            # 保存到文件
            session_file = self.sessions_dir / f"{session_name}.json"
            if FileUtils.save_json(session_data, session_file):
                self.show_info("成功", f"会话 '{session_name}' 保存成功")
                self.current_session = session_name
                self._refresh_history_list()
                
                # 发布会话保存事件
                self.publish_event(EventTypes.SESSION_SAVED, {
                    'session_name': session_name,
                    'session_file': str(session_file)
                })
            else:
                self.show_error("错误", "保存会话失败")
                
        except Exception as e:
            self.show_error("错误", f"保存会话时出错: {str(e)}")
    
    def _collect_session_data(self) -> Dict[str, Any]:
        """收集当前会话数据"""
        # 这里应该收集各个模块的状态数据
        # 暂时返回基本信息
        return {
            'timestamp': datetime.now().isoformat(),
            'version': '1.0',
            'modules': {
                'data_management': {},
                'model_training': {},
                'visualization': {}
            }
        }
    
    def _new_session(self):
        """新建会话"""
        self.session_name_var.delete(0, 'end')
        self.session_desc_text.delete(1.0, 'end')
        self.current_session = None
        self.session_data = {}
        
        self.show_info("信息", "已创建新会话")
        
        # 发布新会话事件
        self.publish_event(EventTypes.SESSION_NEW, {})
    
    def _clear_session(self):
        """清空会话"""
        from tkinter import messagebox
        if messagebox.askyesno("确认", "确定要清空当前会话吗？"):
            self._new_session()
            
            # 发布会话清空事件
            self.publish_event(EventTypes.SESSION_CLEARED, {})
    
    def _refresh_history_list(self):
        """刷新历史列表"""
        # 清空现有项目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)
        
        # 加载会话文件
        for session_file in self.sessions_dir.glob("*.json"):
            try:
                session_data = FileUtils.load_json(session_file)
                if session_data:
                    self.history_tree.insert('', 'end', values=(
                        session_data.get('name', session_file.stem),
                        session_data.get('created_time', '未知'),
                        session_data.get('modified_time', '未知'),
                        session_data.get('description', '无描述')[:50]
                    ), tags=(str(session_file),))
            except Exception as e:
                self.logger.warning(f"加载会话文件失败 {session_file}: {e}")
    
    def _on_history_select(self, event):
        """历史选择事件处理"""
        selection = self.history_tree.selection()
        if selection:
            self.load_button.config(state='normal')
            self.delete_button.config(state='normal')
        else:
            self.load_button.config(state='disabled')
            self.delete_button.config(state='disabled')
    
    def _load_selected_session(self):
        """加载选中的会话"""
        selection = self.history_tree.selection()
        if not selection:
            return
        
        try:
            item = selection[0]
            tags = self.history_tree.item(item, 'tags')
            if tags:
                session_file = Path(tags[0])
                session_data = FileUtils.load_json(session_file)
                
                if session_data:
                    # 加载会话数据
                    self.session_name_var.delete(0, 'end')
                    self.session_name_var.insert(0, session_data.get('name', ''))
                    
                    self.session_desc_text.delete(1.0, 'end')
                    self.session_desc_text.insert(1.0, session_data.get('description', ''))
                    
                    self.current_session = session_data.get('name')
                    self.session_data = session_data.get('data', {})
                    
                    self.show_info("成功", f"会话 '{self.current_session}' 加载成功")
                    
                    # 发布会话加载事件
                    self.publish_event(EventTypes.SESSION_LOADED, {
                        'session_name': self.current_session,
                        'session_data': self.session_data
                    })
                else:
                    self.show_error("错误", "加载会话数据失败")
                    
        except Exception as e:
            self.show_error("错误", f"加载会话时出错: {str(e)}")
    
    def _delete_selected_session(self):
        """删除选中的会话"""
        selection = self.history_tree.selection()
        if not selection:
            return
        
        from tkinter import messagebox
        if not messagebox.askyesno("确认删除", "确定要删除选中的会话吗？"):
            return
        
        try:
            item = selection[0]
            tags = self.history_tree.item(item, 'tags')
            if tags:
                session_file = Path(tags[0])
                session_file.unlink()
                
                self.show_info("成功", "会话删除成功")
                self._refresh_history_list()
                
        except Exception as e:
            self.show_error("错误", f"删除会话时出错: {str(e)}")
    
    def _export_session(self):
        """导出会话"""
        if not self.current_session:
            self.show_warning("警告", "请先保存当前会话")
            return
        
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="导出会话",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if filename:
                session_file = self.sessions_dir / f"{self.current_session}.json"
                session_data = FileUtils.load_json(session_file)
                
                if session_data and FileUtils.save_json(session_data, filename):
                    self.show_info("成功", f"会话导出到: {filename}")
                else:
                    self.show_error("错误", "导出会话失败")
                    
        except Exception as e:
            self.show_error("错误", f"导出会话时出错: {str(e)}")
    
    def _import_session(self):
        """导入会话"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                title="导入会话",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if filename:
                session_data = FileUtils.load_json(filename)
                
                if session_data:
                    # 导入会话数据
                    session_name = session_data.get('name', Path(filename).stem)
                    session_file = self.sessions_dir / f"{session_name}.json"
                    
                    if FileUtils.save_json(session_data, session_file):
                        self.show_info("成功", f"会话 '{session_name}' 导入成功")
                        self._refresh_history_list()
                    else:
                        self.show_error("错误", "保存导入的会话失败")
                else:
                    self.show_error("错误", "读取会话文件失败")
                    
        except Exception as e:
            self.show_error("错误", f"导入会话时出错: {str(e)}")
    
    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        # 更新会话数据
        if 'data_management' not in self.session_data:
            self.session_data['data_management'] = {}
        
        self.session_data['data_management']['last_loaded'] = {
            'timestamp': datetime.now().isoformat(),
            'data_info': event_data
        }
    
    def _on_model_trained(self, event_data):
        """模型训练事件处理"""
        # 更新会话数据
        if 'model_training' not in self.session_data:
            self.session_data['model_training'] = {}
        
        self.session_data['model_training']['last_training'] = {
            'timestamp': datetime.now().isoformat(),
            'training_info': event_data
        }
    
    def _on_session_changed(self, event_data):
        """会话变更事件处理"""
        # 处理会话变更
        pass
    
    def get_current_session(self) -> Optional[str]:
        """获取当前会话名称"""
        return self.current_session
    
    def get_session_data(self) -> Dict[str, Any]:
        """获取会话数据"""
        return self.session_data.copy()
