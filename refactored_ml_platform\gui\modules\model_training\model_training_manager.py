#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练管理器
整合模型训练的各个功能模块
"""

import tkinter as tk
from typing import Dict, Any, Optional

from ...core.base_gui import BaseGUI
from ...core.component_factory import get_component_factory
from ...core.event_manager import get_event_manager, EventTypes
from ...core.model_manager import get_model_manager

# 导入算法模块
try:
    from ....algorithms import MODEL_TRAINERS, MODEL_NAMES, MODEL_DISPLAY_NAMES
    HAS_ALGORITHMS = True
except ImportError:
    MODEL_TRAINERS = {}
    MODEL_NAMES = []
    MODEL_DISPLAY_NAMES = {}
    HAS_ALGORITHMS = False


class ModelTrainingManager(BaseGUI):
    """模型训练管理器类"""
    
    def __init__(self, parent):
        """初始化模型训练管理器"""
        self.current_data = None
        self.training_results = {}
        self.model_manager = get_model_manager()

        super().__init__(parent)

        # 订阅相关事件
        self._bind_events()
    
    def _setup_ui(self):
        """设置UI界面"""
        factory = get_component_factory()
        
        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        else:
            self.main_frame = None
            return
        
        # 标题
        title_frame = factory.create_frame(self.main_frame)
        title_frame.pack(fill='x', pady=(0, 10))
        
        factory.create_label(title_frame, text="模型训练", style='title').pack(anchor='w')
        factory.create_label(title_frame, text="机器学习模型的训练、比较和优化", 
                           style='secondary').pack(anchor='w', pady=(5, 0))
        
        # 创建标签页
        self.notebook = factory.create_notebook(self.main_frame)
        self.notebook.pack(fill='both', expand=True)
        
        # 创建功能标签页
        self._create_training_tabs()
    
    def _create_training_tabs(self):
        """创建训练相关标签页"""
        factory = get_component_factory()
        
        # 模型训练标签页
        trainer_frame = factory.create_frame(self.notebook)
        self.notebook.add(trainer_frame, text="模型训练")
        
        # 添加模型训练内容
        self._create_trainer_content(trainer_frame)
        
        # 模型比较标签页
        comparison_frame = factory.create_frame(self.notebook)
        self.notebook.add(comparison_frame, text="模型比较")
        
        # 添加模型比较内容
        self._create_comparison_content(comparison_frame)
        
        # 超参数调优标签页
        tuning_frame = factory.create_frame(self.notebook)
        self.notebook.add(tuning_frame, text="超参数调优")
        
        # 添加超参数调优内容
        self._create_tuning_content(tuning_frame)
    
    def _create_trainer_content(self, parent):
        """创建模型训练内容"""
        factory = get_component_factory()
        
        # 主容器
        main_container = factory.create_frame(parent)
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # 标题
        title_label = factory.create_label(main_container, text="模型训练", style='title')
        title_label.pack(pady=(0, 20))
        
        # 状态信息
        status_frame = factory.create_labelframe(main_container, text="训练状态", style='section')
        status_frame.pack(fill='x', pady=(0, 20))
        
        self.status_label = factory.create_label(status_frame, text="等待数据加载...", style='info')
        self.status_label.pack(padx=10, pady=10)
        
        # 模型选择
        model_frame = factory.create_labelframe(main_container, text="模型选择", style='section')
        model_frame.pack(fill='x', pady=(0, 20))
        
        # 可用模型列表
        models_text = "可用模型：\n• 决策树 (Decision Tree)\n• 随机森林 (Random Forest)\n• 逻辑回归 (Logistic Regression)\n• 支持向量机 (SVM)\n• K近邻 (KNN)\n• 朴素贝叶斯 (Naive Bayes)\n• 神经网络 (Neural Network)"
        
        models_label = factory.create_label(model_frame, text=models_text, style='secondary')
        models_label.pack(padx=10, pady=10, anchor='w')
        
        # 训练按钮
        button_frame = factory.create_frame(main_container)
        button_frame.pack(fill='x', pady=(0, 20))
        
        self.train_button = factory.create_button(
            button_frame, 
            text="开始训练", 
            command=self._start_training,
            style='primary'
        )
        self.train_button.pack(pady=10)
        self.train_button.config(state='disabled')
        
        # 结果显示区域
        results_frame = factory.create_labelframe(main_container, text="训练结果", style='section')
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        scrollbar = tk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.config(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill='both', expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill='y', padx=(0, 10), pady=10)
        
        self.results_text.insert('1.0', "训练结果将在这里显示...")
        self.results_text.config(state='disabled')
    
    def _create_comparison_content(self, parent):
        """创建模型比较内容"""
        factory = get_component_factory()
        
        container = factory.create_frame(parent)
        container.pack(fill='both', expand=True, padx=20, pady=20)
        
        title_label = factory.create_label(container, text="模型比较分析", style='title')
        title_label.pack(pady=(0, 20))
        
        info_text = "模型比较功能包括：\n\n• 性能指标对比\n• ROC曲线比较\n• 混淆矩阵对比\n• 特征重要性分析\n• 训练时间比较\n\n请先完成模型训练，然后在此查看比较结果。"
        
        info_label = factory.create_label(container, text=info_text, style='secondary')
        info_label.pack(anchor='w')
    
    def _create_tuning_content(self, parent):
        """创建超参数调优内容"""
        factory = get_component_factory()
        
        container = factory.create_frame(parent)
        container.pack(fill='both', expand=True, padx=20, pady=20)
        
        title_label = factory.create_label(container, text="超参数调优", style='title')
        title_label.pack(pady=(0, 20))
        
        info_text = "超参数调优功能包括：\n\n• 网格搜索 (Grid Search)\n• 随机搜索 (Random Search)\n• 贝叶斯优化\n• 交叉验证\n• 参数重要性分析\n\n该功能将帮助您找到最优的模型参数组合。"
        
        info_label = factory.create_label(container, text=info_text, style='secondary')
        info_label.pack(anchor='w')
    
    def _bind_events(self):
        """绑定事件"""
        event_manager = get_event_manager()
        event_manager.subscribe(EventTypes.DATA_LOADED, self._on_data_loaded)
        event_manager.subscribe(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)
    
    def _start_training(self):
        """开始训练"""
        if not self.current_data:
            self.show_warning("警告", "请先加载数据！")
            return
        
        # 更新状态
        self.status_label.config(text="正在训练模型...")
        self.train_button.config(state='disabled')
        
        # 模拟训练过程
        self._simulate_training()
    
    def _simulate_training(self):
        """模拟训练过程"""
        import threading
        import time
        
        def train():
            try:
                # 模拟训练时间
                time.sleep(2)
                
                # 模拟训练结果
                results = {
                    'DecisionTree': {'accuracy': 0.85, 'f1_score': 0.83},
                    'RandomForest': {'accuracy': 0.89, 'f1_score': 0.87},
                    'LogisticRegression': {'accuracy': 0.82, 'f1_score': 0.80},
                    'SVM': {'accuracy': 0.86, 'f1_score': 0.84}
                }
                
                # 在主线程中更新UI
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._training_completed(results))
                
            except Exception as e:
                if self.main_frame:
                    self.main_frame.after(0, lambda: self._training_failed(str(e)))
        
        # 在后台线程中运行训练
        training_thread = threading.Thread(target=train)
        training_thread.daemon = True
        training_thread.start()
    
    def _training_completed(self, results):
        """训练完成"""
        self.training_results = results
        
        # 更新状态
        self.status_label.config(text=f"训练完成！成功训练 {len(results)} 个模型")
        self.train_button.config(state='normal')
        
        # 显示结果
        self.results_text.config(state='normal')
        self.results_text.delete('1.0', 'end')
        
        result_text = "训练结果：\n\n"
        for model, metrics in results.items():
            result_text += f"{model}:\n"
            result_text += f"  准确率: {metrics['accuracy']:.3f}\n"
            result_text += f"  F1分数: {metrics['f1_score']:.3f}\n\n"
        
        self.results_text.insert('1.0', result_text)
        self.results_text.config(state='disabled')
        
        # 发布训练完成事件
        event_manager = get_event_manager()
        event_manager.publish(EventTypes.MODEL_TRAINED, {
            'results': self.training_results
        })
    
    def _training_failed(self, error_message):
        """训练失败"""
        self.status_label.config(text=f"训练失败: {error_message}")
        self.train_button.config(state='normal')
        self.show_error("训练失败", f"模型训练过程中出现错误:\n{error_message}")
    
    def _on_data_loaded(self, event_data):
        """数据加载事件处理"""
        try:
            if event_data and 'data' in event_data:
                self.current_data = event_data['data']
                self.status_label.config(text=f"✅ 数据已加载: {self.current_data.shape[0]} 行, {self.current_data.shape[1]} 列")
                self.train_button.config(state='normal')
                self.logger.info(f"模型训练模块已加载数据: {self.current_data.shape}")
            else:
                self.logger.warning("数据加载事件中缺少'data'字段")
                self.status_label.config(text="❌ 数据加载失败")
        except Exception as e:
            self.logger.error(f"处理数据加载事件时出错: {e}")
            self.status_label.config(text="❌ 数据加载失败")
    
    def _on_data_preprocessed(self, event_data):
        """数据预处理事件处理"""
        try:
            if event_data and all(key in event_data for key in ['X_train', 'X_test', 'y_train', 'y_test']):
                # 更新状态显示训练和测试数据已准备好
                train_size = event_data['X_train'].shape[0]
                test_size = event_data['X_test'].shape[0]
                feature_count = event_data['X_train'].shape[1]
                
                self.status_label.config(
                    text=f"✅ 数据已预处理 (训练集: {train_size} 行, 测试集: {test_size} 行, 特征: {feature_count} 个)"
                )
                self.train_button.config(state='normal')
                self.logger.info(f"模型训练模块已接收预处理数据: 训练集{train_size}行, 测试集{test_size}行")
            elif event_data and 'data' in event_data:
                # 如果只有原始数据，显示预处理完成但未分割
                data = event_data['data']
                self.status_label.config(text=f"✅ 数据已预处理 ({data.shape[0]} 行, {data.shape[1]} 列) - 请手动进行数据分割")
                self.logger.info(f"模型训练模块已接收预处理数据: {data.shape}")
            else:
                self.logger.warning("数据预处理事件中缺少必要字段")
                self.status_label.config(text="❌ 数据预处理失败")
        except Exception as e:
            self.logger.error(f"处理数据预处理事件时出错: {e}")
            self.status_label.config(text="❌ 数据预处理失败")
    
    def get_current_data(self) -> Optional[Dict[str, Any]]:
        """获取当前数据"""
        return {'dataframe': self.current_data} if self.current_data is not None else None
    
    def get_training_results(self) -> Dict[str, Any]:
        """获取训练结果"""
        return self.training_results.copy()
    
    def clear_results(self):
        """清空所有结果"""
        self.training_results = {}
        if hasattr(self, 'results_text'):
            self.results_text.config(state='normal')
            self.results_text.delete('1.0', 'end')
            self.results_text.insert('1.0', "训练结果将在这里显示...")
            self.results_text.config(state='disabled')
