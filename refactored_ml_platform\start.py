#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习平台启动脚本
使用您原有的完整GUI设计
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🚀 启动重构版多模型集成机器学习平台...")

    try:
        # 导入主程序
        from main import RefactoredMLPlatform

        print("✓ 正在初始化应用程序...")

        # 创建并运行应用程序
        app = RefactoredMLPlatform()
        print("✓ 应用程序初始化完成")
        print("✓ 启动GUI界面...")

        app.run()

    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("尝试使用备用启动方式...")

        try:
            # 备用启动方式
            import tkinter as tk
            from gui.layouts.main_layout import MainWindow

            print("✓ 使用备用GUI启动...")

            root = tk.Tk()
            root.title("多模型集成机器学习平台 - 重构版")
            root.geometry("1400x900")

            # 创建主界面
            app = MainWindow(root)
            print("✓ GUI启动成功")

            # 启动GUI
            root.mainloop()

        except Exception as backup_e:
            print(f"❌ 备用启动也失败: {backup_e}")
            show_cli_help()

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        show_cli_help()

def show_cli_help():
    """显示CLI帮助信息"""
    print("\n" + "="*50)
    print("GUI启动失败，您可以使用CLI模式:")
    print("="*50)
    print()
    print("1. 查看CLI帮助:")
    print("   python -m cli.main --help")
    print()
    print("2. 训练所有模型:")
    print("   python -m cli.main --data data.csv --target target --mode train --model All")
    print()
    print("3. 一键完整分析:")
    print("   python -m cli.pipeline --data data.csv --target target --strategy balanced")
    print()
    print("4. 生成可视化:")
    print("   python -m cli.main --data data.csv --target target --mode plot --model XGBoost")
    print()
    print("5. 生成报告:")
    print("   python -m cli.main --data data.csv --target target --mode report --model All")
    print()
    print("="*50)



if __name__ == '__main__':
    main()
